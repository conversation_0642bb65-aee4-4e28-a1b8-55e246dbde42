POST
​/legal​/crawl​/getCrawlHistory
查询执行网页的历史记录

Parameters
Cancel
No parameters

Request body

application/json
{
  "current": 0,
  "detailId": "string",
  "size": 0,
  "sortRules": "string"
}
Execute
Responses
Code	Description	Links
200	
OK

Media type

*/*
Controls Accept header.
Example Value
Schema
{
  "data": {
    "countId": "string",
    "current": 0,
    "hitCount": true,
    "maxLimit": 0,
    "optimizeCountSql": true,
    "orders": [
      {
        "asc": true,
        "column": "string"
      }
    ],
    "pages": 0,
    "records": [
      {
        "articleTitle": "string",
        "createdTime": "2025-08-28T07:09:12.647Z",
        "detailId": "string",
        "detailUrl": "string",
        "exceptionMsg": "string",
        "id": "string",
        "processStatus": "PROCESS_FAIL"
      }
    ],
    "searchCount": true,
    "size": 0,
    "total": 0
  },
  "message": "string",
  "status": 0,
  "success": true,
  "timestamp": 0
}